import 'dart:io';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import '../services/supabase_storage_service.dart';

/// Reusable shop logo widget with upload functionality
class ShopLogoWidget extends StatefulWidget {
  final String? userId;
  final String? shopId;
  final String? currentLogoUrl;
  final double size;
  final bool isEditable;
  final Function(String?) onImageChanged;
  final Function(String)? onError;
  final Color? backgroundColor;
  final Color? borderColor;
  final double borderWidth;

  const ShopLogoWidget({
    super.key,
    this.userId,
    this.shopId,
    this.currentLogoUrl,
    this.size = 120,
    this.isEditable = true,
    required this.onImageChanged,
    this.onError,
    this.backgroundColor,
    this.borderColor,
    this.borderWidth = 2,
  });

  @override
  State<ShopLogoWidget> createState() => _ShopLogoWidgetState();
}

class _ShopLogoWidgetState extends State<ShopLogoWidget> {
  bool _isUploading = false;
  final ImagePicker _picker = ImagePicker();

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: widget.isEditable && widget.userId != null ? _pickAndUploadImage : null,
      child: Container(
        width: widget.size,
        height: widget.size,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: widget.backgroundColor ?? Colors.grey[200],
          border: Border.all(
            color: widget.borderColor ?? Colors.grey[300]!,
            width: widget.borderWidth,
          ),
        ),
        child: Stack(
          children: [
            // Logo image
            ClipOval(
              child: widget.currentLogoUrl != null && widget.currentLogoUrl!.isNotEmpty
                  ? Image.network(
                      widget.currentLogoUrl!,
                      width: widget.size,
                      height: widget.size,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) {
                        return _buildPlaceholder();
                      },
                      loadingBuilder: (context, child, loadingProgress) {
                        if (loadingProgress == null) return child;
                        return _buildLoadingIndicator();
                      },
                    )
                  : _buildPlaceholder(),
            ),

            // Upload indicator
            if (_isUploading)
              Container(
                width: widget.size,
                height: widget.size,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: Colors.black.withOpacity(0.5),
                ),
                child: const Center(
                  child: CircularProgressIndicator(
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                ),
              ),

            // Edit icon
            if (widget.isEditable && widget.userId != null && !_isUploading)
              Positioned(
                bottom: 0,
                right: 0,
                child: Container(
                  width: widget.size * 0.3,
                  height: widget.size * 0.3,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: Theme.of(context).primaryColor,
                    border: Border.all(
                      color: Colors.white,
                      width: 2,
                    ),
                  ),
                  child: Icon(
                    Icons.camera_alt,
                    color: Colors.white,
                    size: widget.size * 0.15,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildPlaceholder() {
    return Container(
      width: widget.size,
      height: widget.size,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: widget.backgroundColor ?? Colors.grey[200],
      ),
      child: Icon(
        Icons.store,
        size: widget.size * 0.4,
        color: Colors.grey[400],
      ),
    );
  }

  Widget _buildLoadingIndicator() {
    return Container(
      width: widget.size,
      height: widget.size,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: Colors.grey[200],
      ),
      child: const Center(
        child: CircularProgressIndicator(),
      ),
    );
  }

  Future<void> _pickAndUploadImage() async {
    if (_isUploading) return;

    try {
      final XFile? pickedFile = await _picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 512,
        maxHeight: 512,
        imageQuality: 90,
      );

      if (pickedFile == null) return;

      setState(() {
        _isUploading = true;
      });

      final imageFile = File(pickedFile.path);

      try {
        final result = await SupabaseStorageService.uploadShopLogo(
          userId: widget.userId!,
          imageFile: imageFile,
          shopId: widget.shopId,
          replaceExisting: true,
        );

        if (result.isSuccess && result.imageUrl != null) {
          widget.onImageChanged(result.imageUrl);

          // Show success feedback
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: const Row(
                  children: [
                    Icon(Icons.check_circle, color: Colors.white),
                    SizedBox(width: 8),
                    Text('Shop logo updated successfully!'),
                  ],
                ),
                backgroundColor: Colors.green,
                behavior: SnackBarBehavior.floating,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
                duration: const Duration(milliseconds: 2000),
              ),
            );
          }
        } else {
          widget.onError?.call(result.error ?? 'Failed to upload logo');
        }
      } catch (e) {
        widget.onError?.call('Upload failed: $e');
      }
    } catch (e) {
      widget.onError?.call('Failed to pick image: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isUploading = false;
        });
      }
    }
  }
}
