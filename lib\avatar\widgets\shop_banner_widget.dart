import 'dart:io';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import '../services/supabase_storage_service.dart';

/// Reusable shop banner widget with upload functionality
class ShopBannerWidget extends StatefulWidget {
  final String? userId;
  final String? shopId;
  final String? currentBannerUrl;
  final double width;
  final double height;
  final bool isEditable;
  final Function(String?) onImageChanged;
  final Function(String)? onError;
  final Color? backgroundColor;
  final BorderRadius? borderRadius;

  const ShopBannerWidget({
    super.key,
    this.userId,
    this.shopId,
    this.currentBannerUrl,
    this.width = double.infinity,
    this.height = 200,
    this.isEditable = true,
    required this.onImageChanged,
    this.onError,
    this.backgroundColor,
    this.borderRadius,
  });

  @override
  State<ShopBannerWidget> createState() => _ShopBannerWidgetState();
}

class _ShopBannerWidgetState extends State<ShopBannerWidget> {
  bool _isUploading = false;
  final ImagePicker _picker = ImagePicker();

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: widget.isEditable && widget.userId != null ? _pickAndUploadImage : null,
      child: Container(
        width: widget.width,
        height: widget.height,
        decoration: BoxDecoration(
          color: widget.backgroundColor ?? Colors.grey[200],
          borderRadius: widget.borderRadius ?? BorderRadius.circular(12),
        ),
        child: Stack(
          children: [
            // Banner image
            ClipRRect(
              borderRadius: widget.borderRadius ?? BorderRadius.circular(12),
              child: widget.currentBannerUrl != null && widget.currentBannerUrl!.isNotEmpty
                  ? Image.network(
                      widget.currentBannerUrl!,
                      width: widget.width,
                      height: widget.height,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) {
                        return _buildPlaceholder();
                      },
                      loadingBuilder: (context, child, loadingProgress) {
                        if (loadingProgress == null) return child;
                        return _buildLoadingIndicator();
                      },
                    )
                  : _buildPlaceholder(),
            ),

            // Upload indicator
            if (_isUploading)
              Container(
                width: widget.width,
                height: widget.height,
                decoration: BoxDecoration(
                  borderRadius: widget.borderRadius ?? BorderRadius.circular(12),
                  color: Colors.black.withOpacity(0.5),
                ),
                child: const Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      CircularProgressIndicator(
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                      SizedBox(height: 8),
                      Text(
                        'Uploading banner...',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ),
              ),

            // Edit overlay
            if (widget.isEditable && widget.userId != null && !_isUploading)
              Positioned.fill(
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: widget.borderRadius ?? BorderRadius.circular(12),
                    color: Colors.black.withOpacity(0.3),
                  ),
                  child: const Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.camera_alt,
                          color: Colors.white,
                          size: 32,
                        ),
                        SizedBox(height: 8),
                        Text(
                          'Tap to change banner',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildPlaceholder() {
    return Container(
      width: widget.width,
      height: widget.height,
      decoration: BoxDecoration(
        color: widget.backgroundColor ?? Colors.grey[200],
        borderRadius: widget.borderRadius ?? BorderRadius.circular(12),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.image,
            size: 48,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 8),
          Text(
            'Shop Banner',
            style: TextStyle(
              color: Colors.grey[600],
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
          ),
          if (widget.isEditable && widget.userId != null)
            Text(
              'Tap to add banner image',
              style: TextStyle(
                color: Colors.grey[500],
                fontSize: 12,
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildLoadingIndicator() {
    return Container(
      width: widget.width,
      height: widget.height,
      decoration: BoxDecoration(
        color: Colors.grey[200],
        borderRadius: widget.borderRadius ?? BorderRadius.circular(12),
      ),
      child: const Center(
        child: CircularProgressIndicator(),
      ),
    );
  }

  Future<void> _pickAndUploadImage() async {
    if (_isUploading) return;

    try {
      final XFile? pickedFile = await _picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 1920,
        maxHeight: 600,
        imageQuality: 85,
      );

      if (pickedFile == null) return;

      setState(() {
        _isUploading = true;
      });

      final imageFile = File(pickedFile.path);

      try {
        final result = await SupabaseStorageService.uploadShopBanner(
          userId: widget.userId!,
          imageFile: imageFile,
          shopId: widget.shopId,
          replaceExisting: true,
        );

        if (result.isSuccess && result.imageUrl != null) {
          widget.onImageChanged(result.imageUrl);

          // Show success feedback
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: const Row(
                  children: [
                    Icon(Icons.check_circle, color: Colors.white),
                    SizedBox(width: 8),
                    Text('Shop banner updated successfully!'),
                  ],
                ),
                backgroundColor: Colors.green,
                behavior: SnackBarBehavior.floating,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
                duration: const Duration(milliseconds: 2000),
              ),
            );
          }
        } else {
          widget.onError?.call(result.error ?? 'Failed to upload banner');
        }
      } catch (e) {
        widget.onError?.call('Upload failed: $e');
      }
    } catch (e) {
      widget.onError?.call('Failed to pick image: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isUploading = false;
        });
      }
    }
  }
}
