import 'dart:io';
import '../supabase/config.dart';
import '../models/product/product.dart';
import 'supabase_service.dart';
import 'storage_service.dart';

/// Service for managing shops and products
class ShopService extends BaseSupabaseService {
  /// Get user's shop
  static Future<Shop?> getUserShop() async {
    final userId = BaseSupabaseService.client.auth.currentUser?.id;
    if (userId == null) return null;

    return BaseSupabaseService.executeQuery(() async {
      final response =
          await BaseSupabaseService.client
              .from(DatabaseTables.shops)
              .select()
              .eq('owner_id', userId)
              .maybeSingle();

      return response != null ? Shop.fromJson(response) : null;
    });
  }

  /// Create a new shop
  static Future<Shop> createShop({
    required String shopName,
    required String shopDescription,
    String? location,
    String? phone,
    String? email,
    String? websiteUrl,
    String businessType = 'individual',
  }) async {
    final userId = BaseSupabaseService.client.auth.currentUser?.id;
    if (userId == null) throw const SupabaseException('Not authenticated');

    return BaseSupabaseService.executeQuery(() async {
      final response =
          await BaseSupabaseService.client
              .from(DatabaseTables.shops)
              .insert({
                'owner_id': userId,
                'shop_name': shopName,
                'shop_description': shopDescription,
                'location': location,
                'phone': phone,
                'email': email,
                'website_url': websiteUrl,
                'business_type': businessType,
              })
              .select()
              .single();

      return Shop.fromJson(response);
    });
  }

  /// Update shop
  static Future<Shop> updateShop({
    required String shopId,
    String? shopName,
    String? shopDescription,
    String? location,
    String? phone,
    String? email,
    String? websiteUrl,
    String? shopLogoUrl,
    String? shopBannerUrl,
  }) async {
    return BaseSupabaseService.executeQuery(() async {
      final updateData = <String, dynamic>{};
      if (shopName != null) updateData['shop_name'] = shopName;
      if (shopDescription != null) {
        updateData['shop_description'] = shopDescription;
      }
      if (location != null) updateData['location'] = location;
      if (phone != null) updateData['phone'] = phone;
      if (email != null) updateData['email'] = email;
      if (websiteUrl != null) updateData['website_url'] = websiteUrl;
      if (shopLogoUrl != null) updateData['shop_logo_url'] = shopLogoUrl;
      if (shopBannerUrl != null) updateData['shop_banner_url'] = shopBannerUrl;
      updateData['updated_at'] = DateTime.now().toIso8601String();

      final response =
          await BaseSupabaseService.client
              .from(DatabaseTables.shops)
              .update(updateData)
              .eq('id', shopId)
              .select()
              .single();

      return Shop.fromJson(response);
    });
  }

  /// Update shop logo URL only
  static Future<Shop> updateShopLogo({
    required String shopId,
    required String logoUrl,
  }) async {
    return updateShop(shopId: shopId, shopLogoUrl: logoUrl);
  }

  /// Update shop banner URL only
  static Future<Shop> updateShopBanner({
    required String shopId,
    required String bannerUrl,
  }) async {
    return updateShop(shopId: shopId, shopBannerUrl: bannerUrl);
  }

  /// Upload and update shop logo
  static Future<Shop> uploadAndUpdateShopLogo({
    required String shopId,
    required File imageFile,
  }) async {
    final userId = BaseSupabaseService.client.auth.currentUser?.id;
    if (userId == null) throw const SupabaseException('Not authenticated');

    // Upload the image
    final uploadResult = await StorageService.uploadShopLogo(
      userId: userId,
      imageFile: imageFile,
      shopId: shopId,
    );

    if (!uploadResult.isSuccess || uploadResult.data == null) {
      throw SupabaseException(uploadResult.error ?? 'Failed to upload logo');
    }

    // Update the shop with the new logo URL
    return updateShopLogo(shopId: shopId, logoUrl: uploadResult.data!);
  }

  /// Upload and update shop banner
  static Future<Shop> uploadAndUpdateShopBanner({
    required String shopId,
    required File imageFile,
  }) async {
    final userId = BaseSupabaseService.client.auth.currentUser?.id;
    if (userId == null) throw const SupabaseException('Not authenticated');

    // Upload the image
    final uploadResult = await StorageService.uploadShopBanner(
      userId: userId,
      imageFile: imageFile,
      shopId: shopId,
    );

    if (!uploadResult.isSuccess || uploadResult.data == null) {
      throw SupabaseException(uploadResult.error ?? 'Failed to upload banner');
    }

    // Update the shop with the new banner URL
    return updateShopBanner(shopId: shopId, bannerUrl: uploadResult.data!);
  }

  /// Get shop by ID
  static Future<Shop?> getShop(String shopId) async {
    return BaseSupabaseService.executeQuery(() async {
      final response =
          await BaseSupabaseService.client
              .from(DatabaseTables.shops)
              .select()
              .eq('id', shopId)
              .maybeSingle();

      return response != null ? Shop.fromJson(response) : null;
    });
  }

  /// Search shops
  static Future<List<Shop>> searchShops({
    required String query,
    String? location,
    int limit = 20,
    int offset = 0,
  }) async {
    return BaseSupabaseService.executeQuery(() async {
      var queryBuilder = BaseSupabaseService.client
          .from(DatabaseTables.shops)
          .select()
          .eq('is_active', true)
          .textSearch('shop_name,shop_description,location', query);

      if (location != null) {
        queryBuilder = queryBuilder.ilike('location', '%$location%');
      }

      final response = await queryBuilder
          .order('rating', ascending: false)
          .range(offset, offset + limit - 1);

      return response.map((json) => Shop.fromJson(json)).toList();
    });
  }

  /// Get shop products
  static Future<List<Product>> getShopProducts({
    required String shopId,
    String? categoryId,
    String status = 'active',
    int limit = 20,
    int offset = 0,
  }) async {
    return BaseSupabaseService.executeQuery(() async {
      final response = await BaseSupabaseService.executeRPC<List<dynamic>>(
        'get_shop_products',
        {
          'shop_uuid': shopId,
          'category_filter': categoryId,
          'status_filter': status,
          'limit_count': limit,
          'offset_count': offset,
        },
      );

      return response?.map((json) => Product.fromJson(json)).toList() ?? [];
    });
  }

  /// Create product
  static Future<Product> createProduct({
    required String shopId,
    required String name,
    required String description,
    required double price,
    required String categoryId,
    List<String> images = const [],
    int stockQuantity = 0,
    String condition = 'new',
    String? brand,
    List<String> tags = const [],
    Map<String, dynamic>? specifications,
  }) async {
    return BaseSupabaseService.executeQuery(() async {
      final response =
          await BaseSupabaseService.client
              .from(DatabaseTables.products)
              .insert({
                'shop_id': shopId,
                'category_id': categoryId,
                'name': name,
                'description': description,
                'price': price,
                'images': images,
                'stock_quantity': stockQuantity,
                'condition': condition,
                'brand': brand,
                'tags': tags,
                'specifications': specifications ?? {},
                'status': 'active',
              })
              .select()
              .single();

      return Product.fromJson(response);
    });
  }

  /// Update product
  static Future<Product> updateProduct({
    required String productId,
    String? name,
    String? description,
    double? price,
    String? categoryId,
    List<String>? images,
    int? stockQuantity,
    String? condition,
    String? brand,
    List<String>? tags,
    Map<String, dynamic>? specifications,
    String? status,
  }) async {
    return BaseSupabaseService.executeQuery(() async {
      final updateData = <String, dynamic>{};
      if (name != null) updateData['name'] = name;
      if (description != null) updateData['description'] = description;
      if (price != null) updateData['price'] = price;
      if (categoryId != null) updateData['category_id'] = categoryId;
      if (images != null) updateData['images'] = images;
      if (stockQuantity != null) updateData['stock_quantity'] = stockQuantity;
      if (condition != null) updateData['condition'] = condition;
      if (brand != null) updateData['brand'] = brand;
      if (tags != null) updateData['tags'] = tags;
      if (specifications != null) updateData['specifications'] = specifications;
      if (status != null) updateData['status'] = status;
      updateData['updated_at'] = DateTime.now().toIso8601String();

      final response =
          await BaseSupabaseService.client
              .from(DatabaseTables.products)
              .update(updateData)
              .eq('id', productId)
              .select()
              .single();

      return Product.fromJson(response);
    });
  }

  /// Delete product
  static Future<void> deleteProduct(String productId) async {
    return BaseSupabaseService.executeQuery(() async {
      await BaseSupabaseService.client
          .from(DatabaseTables.products)
          .delete()
          .eq('id', productId);
    });
  }

  /// Search products
  static Future<List<Product>> searchProducts({
    String query = '',
    String? categoryId,
    double? minPrice,
    double? maxPrice,
    String? shopId,
    String sortBy = 'relevance',
    int limit = 20,
    int offset = 0,
  }) async {
    return BaseSupabaseService.executeQuery(() async {
      final response = await BaseSupabaseService.executeRPC<List<dynamic>>(
        'search_products',
        {
          'search_query': query,
          'category_filter': categoryId,
          'min_price': minPrice,
          'max_price': maxPrice,
          'shop_filter': shopId,
          'sort_by': sortBy,
          'limit_count': limit,
          'offset_count': offset,
        },
      );

      return response?.map((json) => Product.fromJson(json)).toList() ?? [];
    });
  }

  /// Get trending products
  static Future<List<Product>> getTrendingProducts({
    String timePeriod = '7 days',
    int limit = 10,
  }) async {
    return BaseSupabaseService.executeQuery(() async {
      final response = await BaseSupabaseService.executeRPC<List<dynamic>>(
        'get_trending_products',
        {'time_period': timePeriod, 'limit_count': limit},
      );

      return response?.map((json) => Product.fromJson(json)).toList() ?? [];
    });
  }

  /// Track product view
  static Future<void> trackProductView(String productId) async {
    final userId = BaseSupabaseService.client.auth.currentUser?.id;

    return BaseSupabaseService.executeQuery(() async {
      await BaseSupabaseService.executeRPC('track_product_view', {
        'product_uuid': productId,
        'viewer_id': userId,
      });
    });
  }

  /// Get product categories
  static Future<List<ProductCategory>> getCategories() async {
    return BaseSupabaseService.executeQuery(() async {
      final response = await BaseSupabaseService.client
          .from(DatabaseTables.categories)
          .select()
          .eq('is_active', true)
          .order('sort_order');

      return response.map((json) => ProductCategory.fromJson(json)).toList();
    });
  }
}

/// Shop model
class Shop {
  final String id;
  final String ownerId;
  final String shopName;
  final String? shopDescription;
  final String? shopLogoUrl;
  final String? shopBannerUrl;
  final String businessType;
  final String? location;
  final String? phone;
  final String? email;
  final String? websiteUrl;
  final bool isVerified;
  final bool isActive;
  final double rating;
  final int totalReviews;
  final int totalSales;
  final double totalRevenue;
  final DateTime createdAt;
  final DateTime updatedAt;

  const Shop({
    required this.id,
    required this.ownerId,
    required this.shopName,
    this.shopDescription,
    this.shopLogoUrl,
    this.shopBannerUrl,
    required this.businessType,
    this.location,
    this.phone,
    this.email,
    this.websiteUrl,
    this.isVerified = false,
    this.isActive = true,
    this.rating = 0.0,
    this.totalReviews = 0,
    this.totalSales = 0,
    this.totalRevenue = 0.0,
    required this.createdAt,
    required this.updatedAt,
  });

  factory Shop.fromJson(Map<String, dynamic> json) {
    return Shop(
      id: json['id'] as String,
      ownerId: json['owner_id'] as String,
      shopName: json['shop_name'] as String,
      shopDescription: json['shop_description'] as String?,
      shopLogoUrl: json['shop_logo_url'] as String?,
      shopBannerUrl: json['shop_banner_url'] as String?,
      businessType: json['business_type'] as String,
      location: json['location'] as String?,
      phone: json['phone'] as String?,
      email: json['email'] as String?,
      websiteUrl: json['website_url'] as String?,
      isVerified: json['is_verified'] as bool? ?? false,
      isActive: json['is_active'] as bool? ?? true,
      rating: (json['rating'] as num?)?.toDouble() ?? 0.0,
      totalReviews: json['total_reviews'] as int? ?? 0,
      totalSales: json['total_sales'] as int? ?? 0,
      totalRevenue: (json['total_revenue'] as num?)?.toDouble() ?? 0.0,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );
  }
}

/// Product category model
class ProductCategory {
  final String id;
  final String name;
  final String? description;
  final String? iconUrl;
  final String? imageUrl;
  final String? parentId;
  final int level;
  final String? path;
  final int sortOrder;
  final String colorHex;
  final bool isActive;
  final bool isFeatured;

  const ProductCategory({
    required this.id,
    required this.name,
    this.description,
    this.iconUrl,
    this.imageUrl,
    this.parentId,
    this.level = 0,
    this.path,
    this.sortOrder = 0,
    this.colorHex = '#6B7280',
    this.isActive = true,
    this.isFeatured = false,
  });

  factory ProductCategory.fromJson(Map<String, dynamic> json) {
    return ProductCategory(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String?,
      iconUrl: json['icon_url'] as String?,
      imageUrl: json['image_url'] as String?,
      parentId: json['parent_id'] as String?,
      level: json['level'] as int? ?? 0,
      path: json['path'] as String?,
      sortOrder: json['sort_order'] as int? ?? 0,
      colorHex: json['color_hex'] as String? ?? '#6B7280',
      isActive: json['is_active'] as bool? ?? true,
      isFeatured: json['is_featured'] as bool? ?? false,
    );
  }
}
